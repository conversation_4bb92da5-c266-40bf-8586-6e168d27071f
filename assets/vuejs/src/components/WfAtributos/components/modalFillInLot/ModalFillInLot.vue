<template>
  <div class="modal fade" id="ModalFillInLot" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content modal-content-custom">
        <div class="header">
          <h4 class="modal-title text-title" id="exampleModalLabel">
            Preencher em massa - {{ modalItems.items.length }} itens selecionados
          </h4>
          <button
            type="button"
            class="close"
            @click="closeModal()"
            aria-label="Close"
          >
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body body">
          <div class="list">
            <table class="table-movement">
              <thead class="table-movement-thead">
                <tr>
                  <th style="width: 20%">Part number</th>
                  <th>Descrição</th>
                </tr>
              </thead>
              <tbody class="table-movement-tbody">
                <tr
                  v-for="(item, rowIndex) in modalItems.items"
                  :key="item.part_number"
                  :class="{ 'odd-item': rowIndex % 2 === 0 }"
                >
                  <td style="width: 20%">
                    <strong>
                      {{ item.part_number }}
                    </strong>
                  </td>
                  <td>{{ item.descricao_curta }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="form-fill-in-lot">
            <div class="fill-in-lot-input">
              <div class="label-container">
                <label>
                  <strong>{{
                    modalItems.attr.codigo +
                    ' - ' +
                    modalItems.attr.nomeApresentacao
                  }}</strong>
                </label>
              </div>

              <div class="input-container">
                <ModalFillInLotInput
                  :initialAttr="modalItems.attr"
                  :initialValue="''"
                  @changeValues="changeValues"
                ></ModalFillInLotInput>
              </div>
            </div>

            <div>
              <input type="checkbox" v-model="overwriteAll" /> Sobrescrever
              preenchidos
            </div>
          </div>
        </div>
        <div class="footer">
          <div></div>
          <div>
            <button type="button" class="btn btn-default" @click="closeModal()">
              Cancelar
            </button>
            <button
              type="button"
              class="btn btn-success"
              :disabled="!value || isLoading"
              @click="submit()"
            >
              <span v-if="isLoading" class="spinner-border-sm" role="status" aria-hidden="true"></span>
              <i v-else class="glyphicon glyphicon-ok icon"></i>
              {{ isLoading ? 'Preenchendo...' : 'Preencher' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import 'vue-loading-overlay/dist/vue-loading.css';
import Loading from 'vue-loading-overlay';
import ModalFillInLotInput from './ModalFillInLotInput.vue';

export default {
  name: 'ModalFillInLot',
  components: {
    Loading,
    ModalFillInLotInput,
  },
  props: {
    modalItems: {
      required: true,
      default: Object,
    },
  },
  data() {
    return {
      listitems: [],
      listStatus: [],
      value: '',
      overwriteAll: false,
      isLoading: false, // Novo estado de loading
    };
  },
  methods: {
    closeModal() {
      this.$emit('closeModal');
    },
    async submit() {
      console.log('Iniciando submit, isLoading:', this.isLoading);
      this.isLoading = true;
      try {
        // Usar uma promessa para esperar a conclusão do evento
        await new Promise((resolve, reject) => {
          this.$emit(
            'handleFillInLot',
            {
              value: this.value,
              overwriteAll: this.overwriteAll,
              attr: this.modalItems.attr,
              items: this.modalItems.items,
            },
            { resolve, reject }
          );
        });
        console.log('Submit concluído, isLoading:', this.isLoading);
      } catch (error) {
        console.error('Erro no submit:', error);
      } finally {
        this.isLoading = false;
        console.log('Finalizando submit, isLoading:', this.isLoading);
      }
    },
    changeValues({ value }) {
      this.value = value;
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  /* padding: 25px 15px; */
}

.icon {
  font-size: smaller;
  margin-right: 10px;
}

.text-title {
  color: #8d9296;
  font-weight: 600;
}

.text-body {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}

.footer {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}

.list,
.form-fill-in-lot {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 0 0 10px;
}

.form-fill-in-lot {
  gap: 15px;
}

.table-movement {
  width: 100%;
  border: 1px solid;
}

.table-movement td {
  background: white;
}

.table-movement th,
.table-movement td {
  padding: 10px;
}

.table-movement-thead {
  color: white;
  background-color: #337ab7;
}
.table-movement-tbody {
  display: block;
  max-height: 200px;
  overflow-y: auto;
}

.table-movement-thead,
.table-movement-tbody tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.odd-item td {
  background: #e2e3e5;
}

.fill-in-lot-input {
  width: 100%;
  border: 1px solid #ddd;
}

.input-container,
.label-container {
  padding: 10px;
}

.label-container {
  background-color: #e2e3e5;
  margin-bottom: 1px solid #ddd;
}

.label-container label {
  margin: 0;
}

.input-container {
  /* border-radius: 0 0 5px 5px; */
}
</style>
